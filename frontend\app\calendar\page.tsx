'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  CalendarIcon,
  ClockIcon,
  BuildingOfficeIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';
import { useAuthStore } from '../stores/authStore';
import apiService from '../services/api';

interface CalendarEvent {
  id: string;
  title: string;
  date: string;
  time?: string;
  end_date?: string;
  end_time?: string;
  type: 'hearing' | 'deadline' | 'effective' | 'termination' | 'task' | 'meeting' | 'review' | 'comment' | 'general' | 'reminder' | 'follow_up';
  agency?: string;
  agency_id?: number;
  location?: string;
  description?: string;
  document_id?: number;
  document_title?: string;
  task_id?: number;
  regulation_id?: number;
  url?: string;
  is_all_day?: boolean;
  is_public?: boolean;
}

interface CalendarData {
  documents: {
    not_yet_effective: any[];
    effective: any[];
    terminated: any[];
  };
  events: CalendarEvent[];
  agency_availability: any[];
  view: string;
  date_range: {
    start: string;
    end: string;
  };
}

interface CalendarStats {
  not_yet_effective_count: number;
  effective_count: number;
  terminated_count: number;
  total_count: number;
  agencies_available: number;
  agencies_unavailable: number;
  upcoming_deadlines: number;
  public_hearings: number;
  comment_periods_open: number;
}

const CalendarPage: React.FC = () => {
  const { isAuthenticated, user } = useAuthStore();
  const [calendarData, setCalendarData] = useState<CalendarData | null>(null);
  const [calendarStats, setCalendarStats] = useState<CalendarStats | null>(null);
  const [agencies, setAgencies] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState<'month' | 'week' | 'day'>('month');
  const [filters, setFilters] = useState({
    agency_id: '',
    doc_type: '',
    event_type: ''
  });
  const [deleteLoading, setDeleteLoading] = useState<number | null>(null);

  const handleDeleteEvent = async (eventId: string) => {
    if (!confirm('Are you sure you want to delete this event?')) {
      return;
    }

    try {
      // Extract numeric ID from event ID (e.g., "task_177" -> 177)
      const numericId = extractNumericId(eventId);
      if (!numericId) {
        setError('Invalid event ID');
        return;
      }

      setDeleteLoading(numericId);
      await apiService.deleteCalendarEvent(numericId);
      // Refresh calendar data
      fetchCalendarData();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete event');
    } finally {
      setDeleteLoading(null);
    }
  };

  // Helper function to extract numeric ID from event ID strings like "task_177", "hearing_123", etc.
  const extractNumericId = (eventId: string): number | null => {
    const match = eventId.match(/(\d+)$/);
    return match ? parseInt(match[1], 10) : null;
  };

  const fetchCalendarData = async () => {
    try {
      setLoading(true);

      const startDate = getViewStartDate();
      const endDate = getViewEndDate();

      const params = {
        view,
        from: startDate.toISOString().split('T')[0],
        to: endDate.toISOString().split('T')[0],
        ...filters
      };

      // Fetch both calendar data and stats
      const [calendarResponse, statsResponse] = await Promise.all([
        apiService.getCalendar(params),
        apiService.getCalendarStats()
      ]);

      setCalendarData(calendarResponse);
      setCalendarStats(statsResponse);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch calendar data');
    } finally {
      setLoading(false);
    }
  };

  const fetchAgencies = async () => {
    try {
      const response = isAuthenticated
        ? await apiService.getAgencies({ per_page: 100 })
        : await apiService.getPublicAgencies({ per_page: 100 });
      setAgencies(response.data);
    } catch (err: any) {
      console.error('Failed to fetch agencies:', err);
    }
  };

  useEffect(() => {
    if (!isAuthenticated) {
      window.location.href = '/login';
      return;
    }
    fetchCalendarData();
    fetchAgencies();
  }, [currentDate, view, filters, isAuthenticated]);

  const getViewStartDate = () => {
    const date = new Date(currentDate);
    switch (view) {
      case 'month':
        return new Date(date.getFullYear(), date.getMonth(), 1);
      case 'week':
        const day = date.getDay();
        return new Date(date.setDate(date.getDate() - day));
      case 'day':
        return new Date(date.setHours(0, 0, 0, 0));
      default:
        return date;
    }
  };

  const getViewEndDate = () => {
    const date = new Date(currentDate);
    switch (view) {
      case 'month':
        return new Date(date.getFullYear(), date.getMonth() + 1, 0);
      case 'week':
        const day = date.getDay();
        return new Date(date.setDate(date.getDate() - day + 6));
      case 'day':
        return new Date(date.setHours(23, 59, 59, 999));
      default:
        return date;
    }
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    switch (view) {
      case 'month':
        newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
        break;
      case 'week':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
        break;
      case 'day':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
        break;
    }
    setCurrentDate(newDate);
  };

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'hearing':
        return <BuildingOfficeIcon className="h-4 w-4" />;
      case 'deadline':
        return <ExclamationTriangleIcon className="h-4 w-4" />;
      case 'effective':
        return <CheckCircleIcon className="h-4 w-4" />;
      case 'termination':
        return <XCircleIcon className="h-4 w-4" />;
      case 'task':
      case 'general':
        return <ClockIcon className="h-4 w-4" />;
      case 'meeting':
        return <BuildingOfficeIcon className="h-4 w-4" />;
      case 'review':
        return <DocumentTextIcon className="h-4 w-4" />;
      case 'comment':
        return <DocumentTextIcon className="h-4 w-4" />;
      case 'reminder':
        return <ClockIcon className="h-4 w-4" />;
      case 'follow_up':
        return <ClockIcon className="h-4 w-4" />;
      default:
        return <CalendarIcon className="h-4 w-4" />;
    }
  };

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'hearing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'deadline':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'effective':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'termination':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'task':
      case 'general':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'meeting':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'review':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'comment':
        return 'bg-cyan-100 text-cyan-800 border-cyan-200';
      case 'reminder':
        return 'bg-pink-100 text-pink-800 border-pink-200';
      case 'follow_up':
        return 'bg-teal-100 text-teal-800 border-teal-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDateHeader = () => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      ...(view === 'day' && { day: 'numeric' })
    };
    return currentDate.toLocaleDateString('en-US', options);
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Calendar</h1>
            <p className="text-gray-600">
              Track document deadlines, hearings, and important dates
            </p>
          </div>
          <Link
            href="/calendar/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Event
          </Link>
        </div>

        {/* Calendar Controls */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            {/* Date Navigation */}
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigateDate('prev')}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <ChevronLeftIcon className="h-5 w-5" />
              </button>
              <h2 className="text-xl font-semibold text-gray-900 min-w-[200px] text-center">
                {formatDateHeader()}
              </h2>
              <button
                onClick={() => navigateDate('next')}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <ChevronRightIcon className="h-5 w-5" />
              </button>
            </div>

            {/* View Controls */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentDate(new Date())}
                className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Today
              </button>
              <div className="flex border border-gray-300 rounded-md">
                {(['month', 'week', 'day'] as const).map((viewType) => (
                  <button
                    key={viewType}
                    onClick={() => setView(viewType)}
                    className={`px-3 py-2 text-sm font-medium capitalize ${
                      view === viewType
                        ? 'bg-primary-600 text-white'
                        : 'text-gray-700 hover:bg-gray-50'
                    } ${viewType === 'month' ? 'rounded-l-md' : viewType === 'day' ? 'rounded-r-md' : ''}`}
                  >
                    {viewType}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <select
              value={filters.agency_id}
              onChange={(e) => setFilters(prev => ({ ...prev, agency_id: e.target.value }))}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">All Agencies</option>
              {agencies.map((agency) => (
                <option key={agency.id} value={agency.id.toString()}>
                  {agency.short_name || agency.name}
                </option>
              ))}
            </select>

            <select
              value={filters.doc_type}
              onChange={(e) => setFilters(prev => ({ ...prev, doc_type: e.target.value }))}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">All Document Types</option>
              <option value="rule">Rule</option>
              <option value="proposed_rule">Proposed Rule</option>
              <option value="notice">Notice</option>
            </select>

            <select
              value={filters.event_type}
              onChange={(e) => setFilters(prev => ({ ...prev, event_type: e.target.value }))}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">All Event Types</option>
              <option value="hearing">Public Hearings</option>
              <option value="deadline">Comment Deadlines</option>
              <option value="effective">Effective Dates</option>
              <option value="termination">Termination Dates</option>
              <option value="task">Tasks</option>
              <option value="general">General Events</option>
              <option value="meeting">Meetings</option>
              <option value="review">Reviews</option>
              <option value="comment">Comments</option>
              <option value="reminder">Reminders</option>
              <option value="follow_up">Follow-ups</option>
            </select>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            {error}
          </div>
        )}

        {/* Calendar Content */}
        {loading ? (
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded mb-4"></div>
              <div className="grid grid-cols-7 gap-4 mb-4">
                {[...Array(7)].map((_, i) => (
                  <div key={i} className="h-6 bg-gray-200 rounded"></div>
                ))}
              </div>
              <div className="grid grid-cols-7 gap-4">
                {[...Array(35)].map((_, i) => (
                  <div key={i} className="h-24 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-8">
            {/* Document Status Summary */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center">
                  <ClockIcon className="h-8 w-8 text-yellow-600 mr-3" />
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Not Yet Effective</h3>
                    <p className="text-2xl font-bold text-yellow-600">
                      {calendarStats?.not_yet_effective_count || 0}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center">
                  <CheckCircleIcon className="h-8 w-8 text-green-600 mr-3" />
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Effective</h3>
                    <p className="text-2xl font-bold text-green-600">
                      {calendarStats?.effective_count || 0}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center">
                  <XCircleIcon className="h-8 w-8 text-red-600 mr-3" />
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Terminated</h3>
                    <p className="text-2xl font-bold text-red-600">
                      {calendarStats?.terminated_count || 0}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Events List */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Upcoming Events</h2>
              
              {!calendarData?.events || calendarData.events.length === 0 ? (
                <div className="text-center py-8">
                  <CalendarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No events found</h3>
                  <p className="text-gray-600">No events scheduled for the selected period.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {calendarData.events.map((event) => (
                    <div
                      key={event.id}
                      className={`border rounded-lg p-4 ${getEventTypeColor(event.type)}`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3 flex-1">
                          <div className="flex-shrink-0 mt-1">
                            {getEventTypeIcon(event.type)}
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold">
                              <Link
                                href={`/calendar/${extractNumericId(event.id) || event.id}`}
                                className="hover:underline"
                              >
                                {event.title}
                              </Link>
                            </h4>
                            <p className="text-sm opacity-75 mt-1">
                              {event.description}
                            </p>
                            <div className="flex items-center space-x-4 text-sm opacity-75 mt-2">
                              <span className="flex items-center">
                                <CalendarIcon className="h-4 w-4 mr-1" />
                                {new Date(event.date).toLocaleDateString()}
                                {event.time && ` at ${event.time}`}
                                {event.end_date && event.end_date !== event.date && ` - ${new Date(event.end_date).toLocaleDateString()}`}
                                {event.end_time && event.end_time !== event.time && ` ${event.end_time}`}
                                {event.is_all_day && ' (All Day)'}
                              </span>
                              {event.agency && (
                                <span className="flex items-center">
                                  <BuildingOfficeIcon className="h-4 w-4 mr-1" />
                                  {event.agency}
                                </span>
                              )}
                              {event.location && (
                                <span>{event.location}</span>
                              )}
                              {event.document_title && (
                                <span className="flex items-center">
                                  <DocumentTextIcon className="h-4 w-4 mr-1" />
                                  {event.document_title}
                                </span>
                              )}
                              {event.url && (
                                <a
                                  href={event.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-primary-600 hover:text-primary-800 underline"
                                >
                                  Link
                                </a>
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEventTypeColor(event.type)}`}>
                            {event.type.replace('_', ' ').toUpperCase()}
                          </span>

                          {user && (
                            <div className="flex items-center space-x-1">
                              <Link
                                href={`/calendar/${event.id}`}
                                className="p-1 text-gray-400 hover:text-gray-600"
                                title="View Event"
                              >
                                <EyeIcon className="h-4 w-4" />
                              </Link>
                              <Link
                                href={`/calendar/${event.id}/edit`}
                                className="p-1 text-gray-400 hover:text-blue-600"
                                title="Edit Event"
                              >
                                <PencilIcon className="h-4 w-4" />
                              </Link>
                              <button
                                onClick={() => handleDeleteEvent(event.id)}
                                className="p-1 text-gray-400 hover:text-red-600"
                                title="Delete Event"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default CalendarPage;
