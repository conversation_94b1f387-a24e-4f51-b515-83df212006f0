'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  DocumentTextIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ClockIcon,
  CalendarIcon,
  UserIcon,
  BuildingOfficeIcon,
  TagIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';
import { useAuthStore } from '../stores/authStore';
import { useProceedings } from '../hooks/useProceedings';

const ProceedingsPage: React.FC = () => {
  const { isAuthenticated, user } = useAuthStore();
  const {
    proceedings,
    loading,
    error,
    pagination,
    filters,
    searchTerm,
    setSearchTerm,
    setFilters,
    setPagination,
    fetchProceedings,
    deleteProceeding
  } = useProceedings();

  useEffect(() => {
    fetchProceedings();
  }, [fetchProceedings]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchProceedings();
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters({ ...filters, [key]: value });
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this proceeding?')) return;
    
    try {
      await deleteProceeding(id);
      fetchProceedings();
    } catch (err: any) {
      console.error('Failed to delete proceeding:', err);
    }
  };

  const canEdit = (proceeding: any) => {
    if (!user) return false;
    return user.role === 'admin' || 
           (user.role === 'editor' && proceeding.owner?.id === user.id);
  };

  const canDelete = (proceeding: any) => {
    if (!user) return false;
    return user.role === 'admin';
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Proceedings</h1>
            <p className="text-gray-600">
              Manage formal proceedings and workflow processes
            </p>
          </div>
          {isAuthenticated && user?.role !== 'viewer' && (
            <Link
              href="/proceedings/new"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              New Proceeding
            </Link>
          )}
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <form onSubmit={handleSearch} className="mb-4">
            <div className="flex gap-4">
              <div className="flex-1">
                <div className="relative">
                  <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search proceedings..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>
              <button
                type="submit"
                className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                Search
              </button>
            </div>
          </form>

          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="pending">Pending</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>

            <select
              value={filters.priority}
              onChange={(e) => handleFilterChange('priority', e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">All Priority</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>

            <select
              value={filters.sort}
              onChange={(e) => handleFilterChange('sort', e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="created_at">Created Date</option>
              <option value="initiation_date">Initiation Date</option>
              <option value="name">Name</option>
              <option value="priority">Priority</option>
            </select>

            <select
              value={filters.order}
              onChange={(e) => handleFilterChange('order', e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="desc">Newest First</option>
              <option value="asc">Oldest First</option>
            </select>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            {error}
          </div>
        )}

        {/* Proceedings List */}
        {loading ? (
          <div className="grid grid-cols-1 gap-6">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow-md p-6 animate-pulse">
                <div className="h-6 bg-gray-200 rounded mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded mb-4 w-3/4"></div>
                <div className="flex space-x-4">
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                  <div className="h-4 bg-gray-200 rounded w-24"></div>
                  <div className="h-4 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
            ))}
          </div>
        ) : proceedings.length === 0 ? (
          <div className="text-center py-12">
            <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No proceedings found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm ? 'Try adjusting your search terms or filters.' : 'Get started by creating your first proceeding.'}
            </p>
            {isAuthenticated && user?.role !== 'viewer' && (
              <Link
                href="/proceedings/new"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                New Proceeding
              </Link>
            )}
          </div>
        ) : (
          <div className="space-y-6">
            {proceedings.map((proceeding) => (
              <div key={proceeding.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(proceeding.status)}`}>
                          {proceeding.status?.toUpperCase()}
                        </span>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(proceeding.priority)}`}>
                          {proceeding.priority?.toUpperCase()}
                        </span>
                        <span className="text-xs text-gray-500">
                          ID: {proceeding.unique_id}
                        </span>
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        <Link 
                          href={`/proceedings/${proceeding.id}`}
                          className="hover:text-primary-600 transition-colors"
                        >
                          {proceeding.name}
                        </Link>
                      </h3>
                      <p className="text-gray-600 mb-4 line-clamp-2">
                        {proceeding.description || proceeding.objective || 'No description available'}
                      </p>
                      
                      {/* Progress Bar */}
                      <div className="mb-4">
                        <div className="flex justify-between text-sm text-gray-600 mb-1">
                          <span>Progress</span>
                          <span>{proceeding.progress_percent}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${proceeding.progress_percent}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {proceeding.completed_steps} of {proceeding.total_steps} steps completed
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-500">
                        <div className="space-y-1">
                          {proceeding.owner && (
                            <div className="flex items-center">
                              <UserIcon className="h-4 w-4 mr-1" />
                              <span>Owner: {proceeding.owner.username}</span>
                            </div>
                          )}
                          {proceeding.agency && (
                            <div className="flex items-center">
                              <BuildingOfficeIcon className="h-4 w-4 mr-1" />
                              <span>Agency: {proceeding.agency.name}</span>
                            </div>
                          )}
                          <div className="flex items-center">
                            <CalendarIcon className="h-4 w-4 mr-1" />
                            <span>Initiated: {new Date(proceeding.initiation_date).toLocaleDateString()}</span>
                          </div>
                          {proceeding.planned_start_date && (
                            <div className="flex items-center">
                              <CalendarIcon className="h-4 w-4 mr-1" />
                              <span>Planned Start: {new Date(proceeding.planned_start_date).toLocaleDateString()}</span>
                            </div>
                          )}
                        </div>
                        <div className="space-y-1">
                          {proceeding.planned_end_date && (
                            <div className="flex items-center">
                              <CalendarIcon className="h-4 w-4 mr-1" />
                              <span>Planned End: {new Date(proceeding.planned_end_date).toLocaleDateString()}</span>
                            </div>
                          )}
                          {proceeding.estimated_duration && (
                            <div className="text-xs">
                              <span className="font-medium">Duration:</span> {proceeding.estimated_duration} days
                            </div>
                          )}
                          {proceeding.review_required && (
                            <div className="text-xs">
                              <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                                proceeding.review_completed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {proceeding.review_completed ? 'REVIEW COMPLETED' : 'REVIEW REQUIRED'}
                              </span>
                            </div>
                          )}
                          {proceeding.requires_ifr && (
                            <div className="text-xs">
                              <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                                proceeding.ifr_triggered ? 'bg-red-100 text-red-800' : 'bg-orange-100 text-orange-800'
                              }`}>
                                {proceeding.ifr_triggered ? 'IFR TRIGGERED' : 'IFR REQUIRED'}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Additional metadata */}
                      {(proceeding.tags || proceeding.prp_alignment || proceeding.critical_milestones) && (
                        <div className="mt-3 pt-3 border-t border-gray-100">
                          <div className="flex flex-wrap items-center gap-2 text-xs text-gray-400">
                            {proceeding.prp_alignment && (
                              <span>PRP: {proceeding.prp_alignment}</span>
                            )}
                            {proceeding.critical_milestones && (
                              <span>Milestones: {proceeding.critical_milestones.substring(0, 50)}...</span>
                            )}
                            {!proceeding.is_public && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                PRIVATE
                              </span>
                            )}
                          </div>
                          {proceeding.tags && (
                            <div className="mt-2">
                              <span className="text-xs text-gray-400 mr-2">Tags:</span>
                              {proceeding.tags.split(',').map((tag, index) => (
                                <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 mr-1">
                                  {tag.trim()}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                    
                    {/* Actions */}
                    <div className="flex items-center space-x-2 ml-4">
                      <Link
                        href={`/proceedings/${proceeding.id}`}
                        className="p-2 text-gray-400 hover:text-primary-600 transition-colors"
                        title="View Proceeding"
                      >
                        <EyeIcon className="h-5 w-5" />
                      </Link>
                      {canEdit(proceeding) && (
                        <Link
                          href={`/proceedings/${proceeding.id}/edit`}
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                          title="Edit Proceeding"
                        >
                          <PencilIcon className="h-5 w-5" />
                        </Link>
                      )}
                      {canDelete(proceeding) && (
                        <button
                          onClick={() => handleDelete(proceeding.id)}
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                          title="Delete Proceeding"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {pagination.total_pages > 1 && (
          <div className="flex items-center justify-between mt-8">
            <div className="text-sm text-gray-700">
              Showing {((pagination.page - 1) * pagination.per_page) + 1} to{' '}
              {Math.min(pagination.page * pagination.per_page, pagination.total)} of{' '}
              {pagination.total} results
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                disabled={pagination.page === 1}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <span className="px-3 py-2 text-sm text-gray-700">
                Page {pagination.page} of {pagination.total_pages}
              </span>
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                disabled={pagination.page === pagination.total_pages}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default ProceedingsPage;
