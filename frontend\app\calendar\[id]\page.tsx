'use client'

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  CalendarIcon,
  ClockIcon,
  MapPinIcon,
  LinkIcon,
  DocumentTextIcon,
  BuildingOfficeIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  GlobeAltIcon,
  LockClosedIcon
} from '@heroicons/react/24/outline';
import Layout from '../../components/Layout/Layout';
import { useAuthStore } from '../../stores/authStore';
import apiService from '../../services/api';

interface CalendarEvent {
  id: number;
  title: string;
  description: string;
  type: string;
  start_date: string;
  end_date: string;
  is_all_day: boolean;
  location: string;
  url: string;
  document_id?: number;
  agency_id?: number;
  is_public: boolean;
  created_at: string;
  updated_at: string;
  created_by?: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
  };
  document?: {
    id: number;
    title: string;
  };
  agency?: {
    id: number;
    name: string;
  };
}

const CalendarEventViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const eventId = params.id as string;
  const { user } = useAuthStore();
  const [event, setEvent] = useState<CalendarEvent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    if (eventId) {
      fetchEvent();
    }
  }, [eventId]);

  const fetchEvent = async () => {
    try {
      const response = await apiService.getCalendarEvent(parseInt(eventId));
      setEvent(response.data);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch calendar event');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      setDeleteLoading(true);
      await apiService.deleteCalendarEvent(parseInt(eventId));
      router.push('/calendar');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete calendar event');
    } finally {
      setDeleteLoading(false);
      setShowDeleteConfirm(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'hearing':
        return 'bg-red-100 text-red-800';
      case 'deadline':
        return 'bg-orange-100 text-orange-800';
      case 'effective':
        return 'bg-green-100 text-green-800';
      case 'termination':
        return 'bg-gray-100 text-gray-800';
      case 'meeting':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-purple-100 text-purple-800';
    }
  };

  const canEdit = user && (user.role === 'admin' || (event?.created_by?.id === user.id));

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading calendar event...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !event) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Event Not Found</h1>
            <p className="text-gray-600 mt-2">{error || 'The requested calendar event could not be found.'}</p>
            <Link
              href="/calendar"
              className="inline-flex items-center mt-4 text-primary-600 hover:text-primary-500"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Calendar
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/calendar"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Calendar
          </Link>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{event.title}</h1>
              <div className="flex items-center mt-2 space-x-4">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEventTypeColor(event.type)}`}>
                  {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                </span>
                <span className="flex items-center text-sm text-gray-500">
                  {event.is_public ? (
                    <>
                      <GlobeAltIcon className="h-4 w-4 mr-1" />
                      Public
                    </>
                  ) : (
                    <>
                      <LockClosedIcon className="h-4 w-4 mr-1" />
                      Private
                    </>
                  )}
                </span>
              </div>
            </div>
            
            {canEdit && (
              <div className="flex space-x-2">
                <Link
                  href={`/calendar/${event.id}/edit`}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <PencilIcon className="h-4 w-4 mr-2" />
                  Edit
                </Link>
                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  className="inline-flex items-center px-3 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-white hover:bg-red-50"
                >
                  <TrashIcon className="h-4 w-4 mr-2" />
                  Delete
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Event Details */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Event Details</h2>
          </div>
          
          <div className="px-6 py-4 space-y-6">
            {/* Description */}
            {event.description && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Description</h3>
                <p className="text-gray-900 whitespace-pre-wrap">{event.description}</p>
              </div>
            )}

            {/* Date and Time */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  Start Date & Time
                </h3>
                <p className="text-gray-900">
                  {event.is_all_day ? 
                    new Date(event.start_date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    }) + ' (All day)' :
                    formatDate(event.start_date)
                  }
                </p>
              </div>

              {event.end_date && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <ClockIcon className="h-4 w-4 mr-2" />
                    End Date & Time
                  </h3>
                  <p className="text-gray-900">
                    {event.is_all_day ? 
                      new Date(event.end_date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      }) + ' (All day)' :
                      formatDate(event.end_date)
                    }
                  </p>
                </div>
              )}
            </div>

            {/* Location */}
            {event.location && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <MapPinIcon className="h-4 w-4 mr-2" />
                  Location
                </h3>
                <p className="text-gray-900">{event.location}</p>
              </div>
            )}

            {/* URL */}
            {event.url && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <LinkIcon className="h-4 w-4 mr-2" />
                  URL
                </h3>
                <a
                  href={event.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary-600 hover:text-primary-500 underline"
                >
                  {event.url}
                </a>
              </div>
            )}

            {/* Related Document */}
            {event.document && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <DocumentTextIcon className="h-4 w-4 mr-2" />
                  Related Document
                </h3>
                <Link
                  href={`/documents/${event.document.id}`}
                  className="text-primary-600 hover:text-primary-500 underline"
                >
                  {event.document.title}
                </Link>
              </div>
            )}

            {/* Related Agency */}
            {event.agency && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <BuildingOfficeIcon className="h-4 w-4 mr-2" />
                  Related Agency
                </h3>
                <Link
                  href={`/agencies/${event.agency.id}`}
                  className="text-primary-600 hover:text-primary-500 underline"
                >
                  {event.agency.name}
                </Link>
              </div>
            )}

            {/* Created By */}
            {event.created_by && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Created By</h3>
                <p className="text-gray-900">
                  {event.created_by.first_name} {event.created_by.last_name} (@{event.created_by.username})
                </p>
              </div>
            )}

            {/* Timestamps */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-4 border-t border-gray-200">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Created</h3>
                <p className="text-gray-900">{formatDate(event.created_at)}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Last Updated</h3>
                <p className="text-gray-900">{formatDate(event.updated_at)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3 text-center">
                <TrashIcon className="mx-auto h-12 w-12 text-red-600" />
                <h3 className="text-lg font-medium text-gray-900 mt-2">Delete Calendar Event</h3>
                <p className="text-sm text-gray-500 mt-2">
                  Are you sure you want to delete this calendar event? This action cannot be undone.
                </p>
                <div className="flex justify-center space-x-3 mt-4">
                  <button
                    onClick={() => setShowDeleteConfirm(false)}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                    disabled={deleteLoading}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleDelete}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                    disabled={deleteLoading}
                  >
                    {deleteLoading ? 'Deleting...' : 'Delete'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default CalendarEventViewPage;
