'use client'

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  CheckCircleIcon,
  XMarkIcon,
  BuildingOfficeIcon,
  ArrowLeftIcon,
  PhotoIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../components/Layout/Layout';
import { useAuthStore } from '../../../stores/authStore';
import apiService from '../../../services/api';
import { Agency } from '../../../types';

interface AgencyFormData {
  name: string;
  short_name: string;
  description: string;
  agency_type: string;
  website: string;
  email: string;
  phone: string;
  fax: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
  logo_url: string;
  parent_agency_id: string;
  is_active: boolean;
  established_date: string;
  jurisdiction: string;
  mission_statement: string;
  contact_person: string;
  contact_title: string;
  contact_email: string;
  contact_phone: string;
}

const EditAgencyPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [agency, setAgency] = useState<Agency | null>(null);
  const [parentAgencies, setParentAgencies] = useState<any[]>([]);
  const [formData, setFormData] = useState<AgencyFormData>({
    name: '',
    short_name: '',
    description: '',
    agency_type: 'agency',
    website: '',
    email: '',
    phone: '',
    fax: '',
    address: '',
    city: '',
    state: '',
    zip_code: '',
    country: 'United States',
    logo_url: '',
    parent_agency_id: '',
    is_active: true,
    established_date: '',
    jurisdiction: '',
    mission_statement: '',
    contact_person: '',
    contact_title: '',
    contact_email: '',
    contact_phone: '',
  });

  const agencyId = params?.id as string;

  useEffect(() => {
    if (!isAuthenticated || (user?.role !== 'admin' && user?.role !== 'editor')) {
      router.push('/dashboard');
      return;
    }

    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch agency
        const parsedId = parseInt(agencyId);
        if (isNaN(parsedId) || parsedId <= 0) {
          throw new Error('Invalid agency ID');
        }
        const agencyResponse = await apiService.getAgency(parsedId);
        const agencyData = agencyResponse.data;
        setAgency(agencyData);

        // Populate form with agency data
        setFormData({
          name: agencyData.name || '',
          short_name: agencyData.short_name || '',
          description: agencyData.description || '',
          agency_type: agencyData.agency_type || 'agency',
          website: agencyData.website || '',
          email: agencyData.email || '',
          phone: agencyData.phone || '',
          fax: agencyData.fax || '',
          address: agencyData.address || '',
          city: agencyData.city || '',
          state: agencyData.state || '',
          zip_code: agencyData.zip_code || '',
          country: agencyData.country || 'United States',
          logo_url: agencyData.logo_url || '',
          parent_agency_id: agencyData.parent_agency_id?.toString() || '',
          is_active: agencyData.is_active !== false,
          established_date: agencyData.established_at ? agencyData.established_at.split('T')[0] : '',
          jurisdiction: agencyData.jurisdiction || '',
          mission_statement: agencyData.mission_statement || '',
          contact_person: agencyData.contact_person || '',
          contact_title: agencyData.contact_title || '',
          contact_email: agencyData.contact_email || '',
          contact_phone: agencyData.contact_phone || '',
        });

        // Fetch parent agencies (excluding current agency)
        const parentAgenciesResponse = await apiService.getAgencies({ per_page: 100 });
        setParentAgencies(parentAgenciesResponse.data.filter(a => a.id !== agencyData.id));
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to fetch agency data');
      } finally {
        setLoading(false);
      }
    };

    if (agencyId && !isNaN(parseInt(agencyId)) && parseInt(agencyId) > 0) {
      fetchData();
    } else if (agencyId) {
      setError('Invalid agency ID format');
      setLoading(false);
    }
  }, [agencyId, isAuthenticated, user, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!agency) return;

    try {
      setSaving(true);
      setError('');
      setSuccess('');

      const updateData = {
        ...formData,
        parent_agency_id: formData.parent_agency_id ? parseInt(formData.parent_agency_id) : null,
        established_at: formData.established_date || null,
      };

      await apiService.updateAgency(agency.id, updateData);
      setSuccess('Agency updated successfully!');
      
      // Redirect to agency detail page after a short delay
      setTimeout(() => {
        router.push(`/agencies/${agency.id}`);
      }, 1500);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update agency');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-4"></div>
            <div className="h-6 bg-gray-200 rounded mb-6 w-3/4"></div>
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error && !agency) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <BuildingOfficeIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Agency not found</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Link
              href="/agencies"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Agencies
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href={`/agencies/${agencyId}`}
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Agency
          </Link>
          
          <div className="flex items-center">
            <PencilIcon className="h-8 w-8 text-primary-600 mr-3" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Edit Agency</h1>
              <p className="text-gray-600 mt-1">Update agency information and details</p>
            </div>
          </div>
        </div>

        {/* Success/Error Messages */}
        {success && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <CheckCircleIcon className="h-5 w-5 text-green-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800">{success}</p>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <XMarkIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Basic Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Agency Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Enter agency name"
                />
              </div>

              <div>
                <label htmlFor="short_name" className="block text-sm font-medium text-gray-700 mb-2">
                  Short Name / Abbreviation
                </label>
                <input
                  type="text"
                  id="short_name"
                  name="short_name"
                  value={formData.short_name}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="e.g., EPA, FDA"
                />
              </div>

              <div className="md:col-span-2">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Brief description of the agency"
                />
              </div>

              <div>
                <label htmlFor="agency_type" className="block text-sm font-medium text-gray-700 mb-2">
                  Agency Type
                </label>
                <select
                  id="agency_type"
                  name="agency_type"
                  value={formData.agency_type}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="agency">Agency</option>
                  <option value="department">Department</option>
                  <option value="bureau">Bureau</option>
                  <option value="office">Office</option>
                  <option value="commission">Commission</option>
                  <option value="board">Board</option>
                  <option value="administration">Administration</option>
                  <option value="service">Service</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label htmlFor="parent_agency_id" className="block text-sm font-medium text-gray-700 mb-2">
                  Parent Agency
                </label>
                <select
                  id="parent_agency_id"
                  name="parent_agency_id"
                  value={formData.parent_agency_id}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">No parent agency</option>
                  {parentAgencies.map((parentAgency) => (
                    <option key={parentAgency.id} value={parentAgency.id}>
                      {parentAgency.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Contact Information</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-2">
                  Website
                </label>
                <input
                  type="url"
                  id="website"
                  name="website"
                  value={formData.website}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="https://www.agency.gov"
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                  Phone
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="(*************"
                />
              </div>

              <div>
                <label htmlFor="fax" className="block text-sm font-medium text-gray-700 mb-2">
                  Fax
                </label>
                <input
                  type="tel"
                  id="fax"
                  name="fax"
                  value={formData.fax}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="(*************"
                />
              </div>
            </div>
          </div>

          {/* Address */}
          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Address</h2>

            <div className="grid grid-cols-1 gap-6">
              <div>
                <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
                  Street Address
                </label>
                <input
                  type="text"
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="1234 Government Way"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="md:col-span-2">
                  <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-2">
                    City
                  </label>
                  <input
                    type="text"
                    id="city"
                    name="city"
                    value={formData.city}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Washington"
                  />
                </div>

                <div>
                  <label htmlFor="state" className="block text-sm font-medium text-gray-700 mb-2">
                    State
                  </label>
                  <input
                    type="text"
                    id="state"
                    name="state"
                    value={formData.state}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="DC"
                  />
                </div>

                <div>
                  <label htmlFor="zip_code" className="block text-sm font-medium text-gray-700 mb-2">
                    ZIP Code
                  </label>
                  <input
                    type="text"
                    id="zip_code"
                    name="zip_code"
                    value={formData.zip_code}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="20001"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-2">
                  Country
                </label>
                <input
                  type="text"
                  id="country"
                  name="country"
                  value={formData.country}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>
          </div>

          {/* Additional Details */}
          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Additional Details</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="established_date" className="block text-sm font-medium text-gray-700 mb-2">
                  Established Date
                </label>
                <input
                  type="date"
                  id="established_date"
                  name="established_date"
                  value={formData.established_date}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              <div>
                <label htmlFor="jurisdiction" className="block text-sm font-medium text-gray-700 mb-2">
                  Jurisdiction
                </label>
                <input
                  type="text"
                  id="jurisdiction"
                  name="jurisdiction"
                  value={formData.jurisdiction}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Federal, State, Local"
                />
              </div>

              <div>
                <label htmlFor="logo_url" className="block text-sm font-medium text-gray-700 mb-2">
                  Logo URL
                </label>
                <input
                  type="url"
                  id="logo_url"
                  name="logo_url"
                  value={formData.logo_url}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="https://www.agency.gov/logo.png"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_active"
                  name="is_active"
                  checked={formData.is_active}
                  onChange={handleChange}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                  Agency is active
                </label>
              </div>

              <div className="md:col-span-2">
                <label htmlFor="mission_statement" className="block text-sm font-medium text-gray-700 mb-2">
                  Mission Statement
                </label>
                <textarea
                  id="mission_statement"
                  name="mission_statement"
                  value={formData.mission_statement}
                  onChange={handleChange}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Agency's mission and purpose"
                />
              </div>
            </div>
          </div>

          {/* Primary Contact */}
          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Primary Contact</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="contact_person" className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Person
                </label>
                <input
                  type="text"
                  id="contact_person"
                  name="contact_person"
                  value={formData.contact_person}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="John Doe"
                />
              </div>

              <div>
                <label htmlFor="contact_title" className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Title
                </label>
                <input
                  type="text"
                  id="contact_title"
                  name="contact_title"
                  value={formData.contact_title}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Director of Public Affairs"
                />
              </div>

              <div>
                <label htmlFor="contact_email" className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Email
                </label>
                <input
                  type="email"
                  id="contact_email"
                  name="contact_email"
                  value={formData.contact_email}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label htmlFor="contact_phone" className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Phone
                </label>
                <input
                  type="tel"
                  id="contact_phone"
                  name="contact_phone"
                  value={formData.contact_phone}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="(*************"
                />
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <Link
              href={`/agencies/${agencyId}`}
              className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={saving}
              className="px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {saving ? 'Updating...' : 'Update Agency'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default EditAgencyPage;
